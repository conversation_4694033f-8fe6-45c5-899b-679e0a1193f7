import { Injectable, signal, PLATFORM_ID, inject } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { BehaviorSubject } from 'rxjs';
import { AudioItem } from '../models/course.model';

@Injectable({
  providedIn: 'root'
})
export class AudioService {
  private platformId = inject(PLATFORM_ID);
  private isBrowser = isPlatformBrowser(this.platformId);
  
  private audioElement: HTMLAudioElement | null = null;
  private currentAudioSubject = new BehaviorSubject<AudioItem | null>(null);
  private isPlayingSubject = new BehaviorSubject<boolean>(false);
  private currentTimeSubject = new BehaviorSubject<number>(0);
  private durationSubject = new BehaviorSubject<number>(0);
  private progressSubject = new BehaviorSubject<number>(0);

  // Signals
  currentAudio = signal<AudioItem | null>(null);
  isPlaying = signal<boolean>(false);
  currentTime = signal<number>(0);
  duration = signal<number>(0);
  progress = signal<number>(0);

  // Observables
  currentAudio$ = this.currentAudioSubject.asObservable();
  isPlaying$ = this.isPlayingSubject.asObservable();
  currentTime$ = this.currentTimeSubject.asObservable();
  duration$ = this.durationSubject.asObservable();
  progress$ = this.progressSubject.asObservable();

  // Mock audio playlist - using online audio samples for demo
  private audioPlaylist: AudioItem[] = [
    {
      id: 'rectilinear-motion',
      title: 'Rectilinear Motion',
      url: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
      duration: 28,
      contentId: 'content-1'
    },
    {
      id: 'projectile-motion',
      title: 'Projectile Motion',
      url: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
      duration: 28,
      contentId: 'content-2'
    },
    {
      id: 'rotation-motion',
      title: 'Rotation Motion',
      url: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
      duration: 28,
      contentId: 'content-3'
    }
  ];

  constructor() {
    if (this.isBrowser) {
      this.initializeAudio();
    }
  }

  private initializeAudio() {
    this.audioElement = new Audio();
    
    this.audioElement.addEventListener('loadedmetadata', () => {
      const duration = this.audioElement?.duration || 0;
      this.duration.set(duration);
      this.durationSubject.next(duration);
    });

    this.audioElement.addEventListener('timeupdate', () => {
      const currentTime = this.audioElement?.currentTime || 0;
      const duration = this.audioElement?.duration || 0;
      const progress = duration > 0 ? (currentTime / duration) * 100 : 0;
      
      this.currentTime.set(currentTime);
      this.progress.set(progress);
      this.currentTimeSubject.next(currentTime);
      this.progressSubject.next(progress);
    });

    this.audioElement.addEventListener('ended', () => {
      this.isPlaying.set(false);
      this.isPlayingSubject.next(false);
      this.playNext();
    });

    this.audioElement.addEventListener('play', () => {
      this.isPlaying.set(true);
      this.isPlayingSubject.next(true);
    });

    this.audioElement.addEventListener('pause', () => {
      this.isPlaying.set(false);
      this.isPlayingSubject.next(false);
    });
  }

  getAudioPlaylist(): AudioItem[] {
    return this.audioPlaylist;
  }

  loadAudio(audioItem: AudioItem) {
    if (!this.audioElement || !this.isBrowser) return;

    this.currentAudio.set(audioItem);
    this.currentAudioSubject.next(audioItem);
    
    this.audioElement.src = audioItem.url;
    this.audioElement.load();
  }

  play() {
    if (!this.audioElement || !this.isBrowser) return;
    
    this.audioElement.play().catch(error => {
      console.warn('Audio play failed:', error);
    });
  }

  pause() {
    if (!this.audioElement || !this.isBrowser) return;
    
    this.audioElement.pause();
  }

  togglePlayPause() {
    if (this.isPlaying()) {
      this.pause();
    } else {
      this.play();
    }
  }

  seekTo(percentage: number) {
    if (!this.audioElement || !this.isBrowser) return;
    
    const duration = this.audioElement.duration;
    if (duration && !isNaN(duration)) {
      this.audioElement.currentTime = (percentage / 100) * duration;
    }
  }

  setVolume(volume: number) {
    if (!this.audioElement || !this.isBrowser) return;
    
    this.audioElement.volume = Math.max(0, Math.min(1, volume));
  }

  playNext() {
    const currentAudio = this.currentAudio();
    if (!currentAudio) return;

    const currentIndex = this.audioPlaylist.findIndex(item => item.id === currentAudio.id);
    const nextIndex = (currentIndex + 1) % this.audioPlaylist.length;
    const nextAudio = this.audioPlaylist[nextIndex];
    
    this.loadAudio(nextAudio);
    setTimeout(() => this.play(), 100);
  }

  playPrevious() {
    const currentAudio = this.currentAudio();
    if (!currentAudio) return;

    const currentIndex = this.audioPlaylist.findIndex(item => item.id === currentAudio.id);
    const prevIndex = currentIndex > 0 ? currentIndex - 1 : this.audioPlaylist.length - 1;
    const prevAudio = this.audioPlaylist[prevIndex];
    
    this.loadAudio(prevAudio);
    setTimeout(() => this.play(), 100);
  }

  playAudioById(audioId: string) {
    const audioItem = this.audioPlaylist.find(item => item.id === audioId);
    if (audioItem) {
      this.loadAudio(audioItem);
      setTimeout(() => this.play(), 100);
    }
  }

  formatTime(seconds: number): string {
    if (!seconds || isNaN(seconds)) return '0:00';
    
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  }

  destroy() {
    if (this.audioElement) {
      this.audioElement.pause();
      this.audioElement.src = '';
      this.audioElement = null;
    }
  }
}
