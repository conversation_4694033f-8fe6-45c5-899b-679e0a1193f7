:host {
  display: block;
  height: 100vh;
  margin: 0;
  padding: 0;
}

.app-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.header {
  background-color: #343a40;
  color: white;
  padding: 12px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 60px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  border-bottom: 1px solid #495057;
}

.header h1 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #ffffff;
}

.bookmarks-btn {
  background-color: #495057;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

.bookmarks-btn:hover {
  background-color: #5a6268;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0,0,0,0.3);
}

.main-content {
  flex: 1;
  display: flex;
  overflow: hidden;
  background-color: #f8f9fa;
}

.sidebar {
  width: 280px;
  background-color: #f8f9fa;
  border-right: 1px solid #dee2e6;
  overflow-y: auto;
  flex-shrink: 0;
  box-shadow: 2px 0 4px rgba(0,0,0,0.05);
}

.content-area {
  flex: 1;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  margin: 0 1px;
  box-shadow: 0 0 8px rgba(0,0,0,0.05);
}

.control-panel {
  width: 300px;
  background-color: #f8f9fa;
  border-left: 1px solid #dee2e6;
  overflow-y: auto;
  flex-shrink: 0;
  box-shadow: -2px 0 4px rgba(0,0,0,0.05);
}