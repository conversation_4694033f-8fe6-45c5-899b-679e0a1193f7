import { Injectable, signal, PLATFORM_ID, inject } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { BehaviorSubject, Observable } from 'rxjs';
import {
  Course,
  Topic,
  SubTopic,
  CoursePage,
  Highlight,
  Bookmark,
  Note,
  AudioItem,
  UserPreferences
} from '../models/course.model';

@Injectable({
  providedIn: 'root'
})
export class CourseService {
  private platformId = inject(PLATFORM_ID);
  private isBrowser = isPlatformBrowser(this.platformId);
  private courseSubject = new BehaviorSubject<Course | null>(null);
  private selectedTopicSubject = new BehaviorSubject<Topic | null>(null);
  private selectedSubTopicSubject = new BehaviorSubject<SubTopic | null>(null);
  private currentPageSubject = new BehaviorSubject<CoursePage | null>(null);
  private highlightsSubject = new BehaviorSubject<Highlight[]>([]);
  private bookmarksSubject = new BehaviorSubject<Bookmark[]>([]);
  private notesSubject = new BehaviorSubject<Note[]>([]);
  private userPreferencesSubject = new BehaviorSubject<UserPreferences>({
    textSize: 'normal',
    highlightColor: '#ffff99'
  });

  // Signals for reactive state management
  course = signal<Course | null>(null);
  selectedTopic = signal<Topic | null>(null);
  selectedSubTopic = signal<SubTopic | null>(null);
  currentPage = signal<CoursePage | null>(null);
  highlights = signal<Highlight[]>([]);
  bookmarks = signal<Bookmark[]>([]);
  notes = signal<Note[]>([]);
  userPreferences = signal<UserPreferences>({
    textSize: 'normal',
    highlightColor: '#ffff99'
  });

  // Observables for components that need them
  course$ = this.courseSubject.asObservable();
  selectedTopic$ = this.selectedTopicSubject.asObservable();
  selectedSubTopic$ = this.selectedSubTopicSubject.asObservable();
  currentPage$ = this.currentPageSubject.asObservable();
  highlights$ = this.highlightsSubject.asObservable();
  bookmarks$ = this.bookmarksSubject.asObservable();
  notes$ = this.notesSubject.asObservable();
  userPreferences$ = this.userPreferencesSubject.asObservable();

  constructor() {
    this.loadMockData();
    this.loadUserData();
  }

  private loadMockData() {
    const mockCourse: Course = {
      id: '1',
      title: 'Physics - Mechanics',
      topics: [
        {
          id: 'dynamics',
          title: 'Dynamics',
          currentPage: 1,
          expanded: true,
          subTopics: [
            {
              id: 'rectilinear-motion',
              title: 'Rectilinear motion (1 of 4)',
              currentPage: 1,
              pages: [
                {
                  id: 'page-1',
                  pageNumber: 1,
                  title: 'Kinematics of Particle',
                  audioUrl: '/assets/audio/rectilinear-motion.mp3',
                  contents: [
                    {
                      id: 'content-1',
                      type: 'text',
                      order: 1,
                      content: `<h3>Kinematics of Particle</h3>
                      <p>It is the study of bodies in motion, it is divided into two parts</p>
                      <p><strong>1. Kinematics</strong> (study of geometry of motion) -</p>
                      <p>It is the study of relation between displacement (s), velocity (v), acceleration (a) and time (t).</p>
                      <p><strong>2. Kinetics</strong> -</p>
                      <p>It is the study of relation between force (f), mass (m), displacement (s), velocity (v), acceleration (a) and time (t).</p>
                      <p><strong>Types of motion based on geometry</strong> -</p>
                      <p><strong>1. Translation</strong> -</p>
                      <p>During the motion of translation, orientation of a body does not change. Translation is of two types :</p>
                      <ul>
                        <li><strong>Rectilinear translation.</strong></li>
                        <li><strong>Curvilinear translation.</strong></li>
                      </ul>
                      <p><strong>2. Rotation</strong> -</p>
                      <p>During the motion of rotation, all the particles will move along concentric circles.</p>`
                    }
                  ]
                }
              ]
            },
            {
              id: 'projectile-motion',
              title: 'Projectile motion',
              currentPage: 1,
              pages: [
                {
                  id: 'page-2',
                  pageNumber: 1,
                  title: 'Projectile Motion Basics',
                  contents: [
                    {
                      id: 'content-2',
                      type: 'text',
                      order: 1,
                      content: '<h3>Projectile Motion</h3><p>Motion of objects under gravity...</p>'
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    };

    this.course.set(mockCourse);
    this.courseSubject.next(mockCourse);
    
    // Set initial selections
    if (mockCourse.topics.length > 0) {
      this.selectTopic(mockCourse.topics[0]);
    }
  }

  private loadUserData() {
    // Load from localStorage or API
    const savedHighlights = localStorage.getItem('highlights');
    const savedBookmarks = localStorage.getItem('bookmarks');
    const savedNotes = localStorage.getItem('notes');
    const savedPreferences = localStorage.getItem('userPreferences');

    if (savedHighlights) {
      const highlights = JSON.parse(savedHighlights);
      this.highlights.set(highlights);
      this.highlightsSubject.next(highlights);
    }

    if (savedBookmarks) {
      const bookmarks = JSON.parse(savedBookmarks);
      this.bookmarks.set(bookmarks);
      this.bookmarksSubject.next(bookmarks);
    }

    if (savedNotes) {
      const notes = JSON.parse(savedNotes);
      this.notes.set(notes);
      this.notesSubject.next(notes);
    }

    if (savedPreferences) {
      const preferences = JSON.parse(savedPreferences);
      this.userPreferences.set(preferences);
      this.userPreferencesSubject.next(preferences);
    }
  }

  selectTopic(topic: Topic) {
    this.selectedTopic.set(topic);
    this.selectedTopicSubject.next(topic);
    
    if (topic.subTopics && topic.subTopics.length > 0) {
      this.selectSubTopic(topic.subTopics[0]);
    } else if (topic.pages && topic.pages.length > 0) {
      this.selectPage(topic.pages[topic.currentPage - 1]);
    }
  }

  selectSubTopic(subTopic: SubTopic) {
    this.selectedSubTopic.set(subTopic);
    this.selectedSubTopicSubject.next(subTopic);
    
    if (subTopic.pages && subTopic.pages.length > 0) {
      this.selectPage(subTopic.pages[subTopic.currentPage - 1]);
    }
  }

  selectPage(page: CoursePage) {
    this.currentPage.set(page);
    this.currentPageSubject.next(page);
  }

  // Highlight management
  addHighlight(highlight: Highlight) {
    const currentHighlights = this.highlights();
    const newHighlights = [...currentHighlights, highlight];
    this.highlights.set(newHighlights);
    this.highlightsSubject.next(newHighlights);
    localStorage.setItem('highlights', JSON.stringify(newHighlights));
  }

  removeHighlight(highlightId: string) {
    const currentHighlights = this.highlights();
    const newHighlights = currentHighlights.filter(h => h.id !== highlightId);
    this.highlights.set(newHighlights);
    this.highlightsSubject.next(newHighlights);
    localStorage.setItem('highlights', JSON.stringify(newHighlights));
  }

  // Bookmark management
  addBookmark(bookmark: Bookmark) {
    const currentBookmarks = this.bookmarks();
    const newBookmarks = [...currentBookmarks, bookmark];
    this.bookmarks.set(newBookmarks);
    this.bookmarksSubject.next(newBookmarks);
    localStorage.setItem('bookmarks', JSON.stringify(newBookmarks));
  }

  removeBookmark(bookmarkId: string) {
    const currentBookmarks = this.bookmarks();
    const newBookmarks = currentBookmarks.filter(b => b.id !== bookmarkId);
    this.bookmarks.set(newBookmarks);
    this.bookmarksSubject.next(newBookmarks);
    localStorage.setItem('bookmarks', JSON.stringify(newBookmarks));
  }

  // Note management
  addNote(note: Note) {
    const currentNotes = this.notes();
    const newNotes = [...currentNotes, note];
    this.notes.set(newNotes);
    this.notesSubject.next(newNotes);
    localStorage.setItem('notes', JSON.stringify(newNotes));
  }

  updateNote(noteId: string, content: string) {
    const currentNotes = this.notes();
    const newNotes = currentNotes.map(note => 
      note.id === noteId ? { ...note, content, timestamp: new Date() } : note
    );
    this.notes.set(newNotes);
    this.notesSubject.next(newNotes);
    localStorage.setItem('notes', JSON.stringify(newNotes));
  }

  deleteNote(noteId: string) {
    const currentNotes = this.notes();
    const newNotes = currentNotes.filter(n => n.id !== noteId);
    this.notes.set(newNotes);
    this.notesSubject.next(newNotes);
    localStorage.setItem('notes', JSON.stringify(newNotes));
  }

  // User preferences
  updateUserPreferences(preferences: Partial<UserPreferences>) {
    const currentPreferences = this.userPreferences();
    const newPreferences = { ...currentPreferences, ...preferences };
    this.userPreferences.set(newPreferences);
    this.userPreferencesSubject.next(newPreferences);
    localStorage.setItem('userPreferences', JSON.stringify(newPreferences));
  }

  // Navigation
  goToNextPage() {
    const currentSubTopic = this.selectedSubTopic();
    if (currentSubTopic && currentSubTopic.pages) {
      const nextPageIndex = currentSubTopic.currentPage;
      if (nextPageIndex < currentSubTopic.pages.length) {
        currentSubTopic.currentPage = nextPageIndex + 1;
        this.selectPage(currentSubTopic.pages[nextPageIndex]);
      }
    }
  }

  goToPreviousPage() {
    const currentSubTopic = this.selectedSubTopic();
    if (currentSubTopic && currentSubTopic.pages) {
      const prevPageIndex = currentSubTopic.currentPage - 2;
      if (prevPageIndex >= 0) {
        currentSubTopic.currentPage = prevPageIndex + 1;
        this.selectPage(currentSubTopic.pages[prevPageIndex]);
      }
    }
  }

  goToPage(pageNumber: number) {
    const currentSubTopic = this.selectedSubTopic();
    if (currentSubTopic && currentSubTopic.pages && pageNumber > 0 && pageNumber <= currentSubTopic.pages.length) {
      currentSubTopic.currentPage = pageNumber;
      this.selectPage(currentSubTopic.pages[pageNumber - 1]);
    }
  }
}
