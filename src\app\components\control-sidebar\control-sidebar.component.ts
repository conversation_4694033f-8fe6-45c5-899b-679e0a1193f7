import { Component, OnInit, ViewChild, ElementRef, AfterViewInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { CourseService } from '../../services/course.service';
import { AudioService } from '../../services/audio.service';
import { SubTopic, UserPreferences, Note, Bookmark, AudioItem } from '../../models/course.model';

@Component({
  selector: 'app-control-sidebar',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="control-sidebar">
      <!-- Page Navigation -->
      <div class="control-section">
        <div class="section-header">
          <span>Page:</span>
          <div class="page-controls">
            @if (selectedSubTopic(); as subTopic) {
              @for (page of getPageNumbers(subTopic); track page) {
                <button 
                  class="page-btn"
                  [class.active]="page === subTopic.currentPage"
                  (click)="goToPage(page)"
                >
                  {{ page }}
                </button>
              }
            }
            <button class="bookmark-btn" (click)="toggleBookmark()" title="Bookmark">
              📖
            </button>
          </div>
        </div>
      </div>

      <!-- Text Size Control -->
      <div class="control-section">
        <div class="section-header">
          <span>Text size:</span>
        </div>
        <div class="text-size-controls">
          <button 
            class="size-btn"
            [class.active]="userPreferences().textSize === 'small'"
            (click)="setTextSize('small')"
          >
            Small
          </button>
          <button 
            class="size-btn"
            [class.active]="userPreferences().textSize === 'normal'"
            (click)="setTextSize('normal')"
          >
            Normal
          </button>
          <button 
            class="size-btn"
            [class.active]="userPreferences().textSize === 'large'"
            (click)="setTextSize('large')"
          >
            Large
          </button>
        </div>
      </div>

      <!-- Now Playing -->
      <div class="control-section">
        <div class="section-header">
          <span>Now playing:</span>
        </div>
        <div class="now-playing">
          @if (currentAudio(); as audio) {
            <div class="audio-item active">
              <div class="audio-info">
                <div class="audio-title">{{ audio.title }}</div>
              </div>
              <div class="audio-controls">
                <button class="audio-control-btn" (click)="toggleAudio()">
                  {{ isPlaying() ? '⏸️' : '▶️' }}
                </button>
                <button class="audio-control-btn" (click)="playNext()">⏭️</button>
              </div>
            </div>
          } @else {
            <div class="no-audio">No audio selected</div>
          }
        </div>
      </div>

      <!-- Audio List -->
      <div class="control-section">
        <div class="section-header">
          <span>Audio List:</span>
        </div>
        <div class="audio-list">
          @for (audioItem of audioPlaylist; track audioItem.id) {
            <div
              class="audio-list-item"
              [class.active]="currentAudio()?.id === audioItem.id"
              (click)="playAudio(audioItem)"
            >
              <div class="audio-item-info">
                <div class="audio-item-title">{{ audioItem.title }}</div>
                <div class="audio-item-duration">{{ formatTime(audioItem.duration) }}</div>
              </div>
              <div class="audio-item-controls">
                @if (currentAudio()?.id === audioItem.id && isPlaying()) {
                  <span class="playing-indicator">🔊</span>
                } @else {
                  <span class="play-indicator">▶️</span>
                }
              </div>
            </div>
          }
        </div>
      </div>

      <!-- Annotate Section -->
      <div class="control-section">
        <div class="section-header">
          <span>Annotate:</span>
        </div>
        <div class="color-picker">
          @for (color of highlightColors; track color.value) {
            <button 
              class="color-btn"
              [style.background-color]="color.value"
              [class.active]="userPreferences().highlightColor === color.value"
              (click)="setHighlightColor(color.value)"
              [title]="color.name"
            ></button>
          }
        </div>
      </div>

      <!-- Notes Section -->
      <div class="control-section notes-section">
        <div class="section-header">
          <span>Notes:</span>
        </div>
        <div class="notes-container">
          <div class="notes-toolbar">
            <button class="toolbar-btn" (click)="formatText('bold')" title="Bold">
              <strong>B</strong>
            </button>
            <button class="toolbar-btn" (click)="formatText('italic')" title="Italic">
              <em>I</em>
            </button>
            <button class="toolbar-btn" (click)="formatText('underline')" title="Underline">
              <u>U</u>
            </button>
            <button class="toolbar-btn" (click)="formatText('strikethrough')" title="Strikethrough">
              <s>S</s>
            </button>
            <button class="toolbar-btn" (click)="insertList()" title="Bullet List">
              •
            </button>
          </div>
          <div 
            class="notes-editor"
            contenteditable="true"
            #notesEditor
            (input)="onNotesChange()"
            (blur)="saveNotes()"
            placeholder="Write your notes here..."
          ></div>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./control-sidebar.component.css']
})
export class ControlSidebarComponent implements OnInit, AfterViewInit {
  @ViewChild('notesEditor') notesEditor!: ElementRef;

  private courseService = inject(CourseService);
  private audioService = inject(AudioService);

  selectedSubTopic = this.courseService.selectedSubTopic;
  userPreferences = this.courseService.userPreferences;
  currentPage = this.courseService.currentPage;
  notes = this.courseService.notes;
  bookmarks = this.courseService.bookmarks;

  // Audio properties
  audioPlaylist = this.audioService.getAudioPlaylist();
  currentAudio = this.audioService.currentAudio;
  isPlaying = this.audioService.isPlaying;

  highlightColors = [
    { name: 'Yellow', value: '#ffff99' },
    { name: 'Green', value: '#90ee90' },
    { name: 'Blue', value: '#87ceeb' },
    { name: 'Pink', value: '#ffb6c1' },
    { name: 'Orange', value: '#ffd700' },
    { name: 'Purple', value: '#dda0dd' }
  ];

  currentNoteContent = '';



  ngOnInit() {
    // Load existing notes for current page
    this.courseService.currentPage$.subscribe(page => {
      if (page) {
        this.loadNotesForPage(page.id);
      }
    });
  }

  ngAfterViewInit() {
    // Initialize notes editor
    if (this.notesEditor) {
      this.loadNotesForCurrentPage();
    }
  }

  getPageNumbers(subTopic: SubTopic): number[] {
    if (!subTopic.pages) return [];
    return Array.from({ length: subTopic.pages.length }, (_, i) => i + 1);
  }

  goToPage(pageNumber: number) {
    this.courseService.goToPage(pageNumber);
  }

  setTextSize(size: 'small' | 'normal' | 'large') {
    this.courseService.updateUserPreferences({ textSize: size });
  }

  setHighlightColor(color: string) {
    this.courseService.updateUserPreferences({ highlightColor: color });
  }

  toggleBookmark() {
    const currentPageData = this.currentPage();
    const selectedTopic = this.courseService.selectedTopic();
    
    if (!currentPageData || !selectedTopic) return;

    const existingBookmark = this.bookmarks().find(
      b => b.pageId === currentPageData.id && b.topicId === selectedTopic.id
    );

    if (existingBookmark) {
      this.courseService.removeBookmark(existingBookmark.id);
    } else {
      const bookmark: Bookmark = {
        id: this.generateId(),
        topicId: selectedTopic.id,
        pageId: currentPageData.id,
        title: `${selectedTopic.title} - Page ${currentPageData.pageNumber}`,
        timestamp: new Date()
      };
      this.courseService.addBookmark(bookmark);
    }
  }

  // Notes functionality
  formatText(command: string) {
    document.execCommand(command, false);
    this.notesEditor.nativeElement.focus();
  }

  insertList() {
    document.execCommand('insertUnorderedList', false);
    this.notesEditor.nativeElement.focus();
  }

  onNotesChange() {
    this.currentNoteContent = this.notesEditor.nativeElement.innerHTML;
  }

  saveNotes() {
    const currentPageData = this.currentPage();
    const selectedTopic = this.courseService.selectedTopic();
    
    if (!currentPageData || !selectedTopic || !this.currentNoteContent.trim()) return;

    const existingNote = this.notes().find(
      n => n.pageId === currentPageData.id && n.topicId === selectedTopic.id
    );

    if (existingNote) {
      this.courseService.updateNote(existingNote.id, this.currentNoteContent);
    } else {
      const note: Note = {
        id: this.generateId(),
        topicId: selectedTopic.id,
        pageId: currentPageData.id,
        content: this.currentNoteContent,
        timestamp: new Date()
      };
      this.courseService.addNote(note);
    }
  }

  private loadNotesForPage(pageId: string) {
    const selectedTopic = this.courseService.selectedTopic();
    if (!selectedTopic) return;

    const pageNote = this.notes().find(
      n => n.pageId === pageId && n.topicId === selectedTopic.id
    );

    if (pageNote && this.notesEditor) {
      this.notesEditor.nativeElement.innerHTML = pageNote.content;
      this.currentNoteContent = pageNote.content;
    } else if (this.notesEditor) {
      this.notesEditor.nativeElement.innerHTML = '';
      this.currentNoteContent = '';
    }
  }

  private loadNotesForCurrentPage() {
    const currentPageData = this.currentPage();
    if (currentPageData) {
      this.loadNotesForPage(currentPageData.id);
    }
  }

  // Audio methods
  toggleAudio() {
    this.audioService.togglePlayPause();
  }

  playNext() {
    this.audioService.playNext();
  }

  playAudio(audioItem: AudioItem) {
    this.audioService.loadAudio(audioItem);
    setTimeout(() => this.audioService.play(), 100);
  }

  formatTime(seconds: number): string {
    return this.audioService.formatTime(seconds);
  }

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }
}
