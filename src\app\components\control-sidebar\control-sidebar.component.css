.control-sidebar {
  height: 100%;
  background-color: #f8f9fa;
  border-left: 1px solid #dee2e6;
  overflow-y: auto;
  font-size: 13px;
}

.control-section {
  padding: 16px 20px;
  border-bottom: 1px solid #f1f3f4;
}

.control-section:last-child {
  border-bottom: none;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

/* Page Controls */
.page-controls {
  display: flex;
  align-items: center;
  gap: 4px;
}

.page-btn {
  width: 32px;
  height: 32px;
  border: 1px solid #ddd;
  background-color: #fff;
  color: #333;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.page-btn:hover {
  background-color: #f8f9fa;
  border-color: #007bff;
}

.page-btn.active {
  background-color: #007bff;
  color: white;
  border-color: #007bff;
}

.bookmark-btn {
  width: 32px;
  height: 32px;
  border: 1px solid #ddd;
  background-color: #fff;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
  margin-left: 8px;
}

.bookmark-btn:hover {
  background-color: #f8f9fa;
  border-color: #28a745;
}

/* Text Size Controls */
.text-size-controls {
  display: flex;
  gap: 4px;
}

.size-btn {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ddd;
  background-color: #fff;
  color: #333;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.size-btn:hover {
  background-color: #f8f9fa;
  border-color: #007bff;
}

.size-btn.active {
  background-color: #007bff;
  color: white;
  border-color: #007bff;
}

/* Now Playing */
.now-playing {
  background-color: #f8f9fa;
  border-radius: 6px;
  padding: 12px;
}

.audio-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}

.audio-item.active {
  color: #007bff;
}

.audio-info {
  flex: 1;
}

.audio-title {
  font-size: 13px;
  font-weight: 500;
  line-height: 1.3;
}

.audio-controls {
  display: flex;
  gap: 4px;
}

.audio-control-btn {
  width: 24px;
  height: 24px;
  border: none;
  background: none;
  cursor: pointer;
  border-radius: 4px;
  font-size: 12px;
  transition: background-color 0.2s ease;
}

.audio-control-btn:hover {
  background-color: #e9ecef;
}

/* Color Picker */
.color-picker {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.color-btn {
  width: 28px;
  height: 28px;
  border: 2px solid #ddd;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.color-btn:hover {
  transform: scale(1.1);
  border-color: #333;
}

.color-btn.active {
  border-color: #333;
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.2);
}

.color-btn.active::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #333;
  font-size: 12px;
  font-weight: bold;
}

/* Notes Section */
.notes-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 300px;
}

.notes-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  border: 1px solid #ddd;
  border-radius: 6px;
  overflow: hidden;
}

.notes-toolbar {
  display: flex;
  gap: 2px;
  padding: 8px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #ddd;
}

.toolbar-btn {
  width: 28px;
  height: 28px;
  border: 1px solid #ddd;
  background-color: #fff;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.toolbar-btn:hover {
  background-color: #e9ecef;
  border-color: #007bff;
}

.toolbar-btn:active {
  background-color: #007bff;
  color: white;
}

.notes-editor {
  flex: 1;
  padding: 12px;
  border: none;
  outline: none;
  font-size: 13px;
  line-height: 1.5;
  color: #333;
  background-color: #fff;
  min-height: 200px;
  resize: none;
}

.notes-editor:empty::before {
  content: attr(placeholder);
  color: #999;
  font-style: italic;
}

.notes-editor:focus {
  background-color: #fafafa;
}

/* Rich text formatting in notes */
.notes-editor strong {
  font-weight: bold;
}

.notes-editor em {
  font-style: italic;
}

.notes-editor u {
  text-decoration: underline;
}

.notes-editor s {
  text-decoration: line-through;
}

.notes-editor ul {
  margin: 8px 0;
  padding-left: 20px;
}

.notes-editor li {
  margin: 4px 0;
}

/* Scrollbar */
.control-sidebar::-webkit-scrollbar {
  width: 6px;
}

.control-sidebar::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.control-sidebar::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.control-sidebar::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Audio List */
.audio-list {
  max-height: 200px;
  overflow-y: auto;
}

.audio-list-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  margin-bottom: 4px;
}

.audio-list-item:hover {
  background-color: #f8f9fa;
}

.audio-list-item.active {
  background-color: #e3f2fd;
  border-left: 3px solid #2196f3;
}

.audio-item-info {
  flex: 1;
}

.audio-item-title {
  font-size: 13px;
  font-weight: 500;
  color: #333;
  margin-bottom: 2px;
}

.audio-item-duration {
  font-size: 11px;
  color: #666;
}

.audio-item-controls {
  font-size: 12px;
}

.playing-indicator {
  color: #2196f3;
  animation: pulse 1.5s infinite;
}

.play-indicator {
  color: #666;
  opacity: 0.7;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.no-audio {
  padding: 12px;
  text-align: center;
  color: #666;
  font-style: italic;
  font-size: 13px;
}

/* Responsive */
@media (max-width: 768px) {
  .control-section {
    padding: 12px 16px;
  }

  .page-controls {
    flex-wrap: wrap;
  }

  .text-size-controls {
    flex-direction: column;
  }

  .color-picker {
    justify-content: center;
  }

  .audio-list {
    max-height: 150px;
  }
}
