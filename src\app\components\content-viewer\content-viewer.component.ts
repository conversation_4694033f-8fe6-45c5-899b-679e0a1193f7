import { Component, OnInit, ElementRef, ViewChild, AfterViewInit, inject, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CourseService } from '../../services/course.service';
import { AudioService } from '../../services/audio.service';
import { CoursePage, Highlight, UserPreferences } from '../../models/course.model';

@Component({
  selector: 'app-content-viewer',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="content-viewer">
      <!-- Audio Player Section -->
      <div class="audio-section">
        <div class="audio-player">
          <div class="audio-info">
            <span class="audio-title">{{ currentAudio()?.title || 'No audio selected' }}</span>
          </div>
          <div class="audio-controls">
            <button class="btn-audio" (click)="toggleAudio()">
              <i class="audio-icon">{{ isPlaying() ? '⏸️' : '▶️' }}</i>
            </button>
            <div class="audio-progress">
              <div class="progress-bar" (click)="seekAudio($event)">
                <div class="progress-fill" [style.width.%]="progress()"></div>
              </div>
              <div class="time-display">
                <span>{{ formatTime(currentTime()) }}</span>
                <span>{{ formatTime(duration()) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Content Section -->
      <div class="content-section">
        @if (currentPage(); as page) {
          <div 
            class="content-body"
            [class]="'text-' + userPreferences().textSize"
            #contentBody
            (mouseup)="handleTextSelection()"
          >
            @for (content of page.contents; track content.id) {
              <div 
                class="content-item"
                [id]="content.id"
                [innerHTML]="content.content"
              ></div>
            }
          </div>
        } @else {
          <div class="no-content">
            <p>Select a topic to view content</p>
          </div>
        }
      </div>

      <!-- Selection Popup -->
      @if (showSelectionPopup) {
        <div 
          class="selection-popup"
          [style.left.px]="popupPosition.x"
          [style.top.px]="popupPosition.y"
        >
          <button 
            class="highlight-btn"
            (click)="highlightSelectedText()"
            title="Highlight text"
          >
            🖍️
          </button>
        </div>
      }
    </div>


  `,
  styleUrls: ['./content-viewer.component.css']
})
export class ContentViewerComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('contentBody') contentBody!: ElementRef;

  private courseService = inject(CourseService);
  private audioService = inject(AudioService);

  currentPage = this.courseService.currentPage;
  userPreferences = this.courseService.userPreferences;
  highlights = this.courseService.highlights;

  // Audio properties from service
  currentAudio = this.audioService.currentAudio;
  isPlaying = this.audioService.isPlaying;
  currentTime = this.audioService.currentTime;
  duration = this.audioService.duration;
  progress = this.audioService.progress;



  // Text selection properties
  showSelectionPopup = false;
  selectedText = '';
  selectedRange: Range | null = null;
  popupPosition = { x: 0, y: 0 };



  ngOnInit() {
    // Subscribe to user preferences changes
    this.courseService.userPreferences$.subscribe(() => {
      this.applyHighlights();
    });

    // Subscribe to page changes
    this.courseService.currentPage$.subscribe(() => {
      setTimeout(() => {
        this.applyHighlights();
      }, 100);
    });

    // Load initial audio
    const audioPlaylist = this.audioService.getAudioPlaylist();
    if (audioPlaylist.length > 0) {
      this.audioService.loadAudio(audioPlaylist[0]);
    }
  }

  ngAfterViewInit() {
    // Apply highlights after view initialization
    setTimeout(() => {
      this.applyHighlights();
    }, 100);
  }

  // Audio methods
  toggleAudio() {
    this.audioService.togglePlayPause();
  }

  seekAudio(event: MouseEvent) {
    const progressBar = event.currentTarget as HTMLElement;
    const rect = progressBar.getBoundingClientRect();
    const percentage = ((event.clientX - rect.left) / rect.width) * 100;
    this.audioService.seekTo(percentage);
  }

  formatTime(seconds: number): string {
    return this.audioService.formatTime(seconds);
  }

  // Text selection methods
  handleTextSelection() {
    const selection = window.getSelection();
    if (selection && selection.toString().trim().length > 0) {
      this.selectedText = selection.toString().trim();
      this.selectedRange = selection.getRangeAt(0);
      this.showSelectionPopup = true;
      
      // Position popup near selection
      const rect = this.selectedRange.getBoundingClientRect();
      this.popupPosition = {
        x: rect.left + rect.width / 2 - 25,
        y: rect.top - 40
      };
    } else {
      this.hideSelectionPopup();
    }
  }

  hideSelectionPopup() {
    this.showSelectionPopup = false;
    this.selectedText = '';
    this.selectedRange = null;
  }

  highlightSelectedText() {
    if (!this.selectedRange || !this.selectedText) return;

    const currentPageData = this.currentPage();
    if (!currentPageData) return;

    // Create highlight object
    const highlight: Highlight = {
      id: this.generateId(),
      topicId: this.courseService.selectedTopic()?.id || '',
      pageId: currentPageData.id,
      contentId: '', // We'll determine this based on the selection
      text: this.selectedText,
      color: this.userPreferences().highlightColor,
      startOffset: this.selectedRange.startOffset,
      endOffset: this.selectedRange.endOffset,
      timestamp: new Date()
    };

    // Add highlight to service
    this.courseService.addHighlight(highlight);

    // Apply highlight visually
    this.applyHighlightToRange(this.selectedRange, highlight.color);

    this.hideSelectionPopup();
  }

  private applyHighlightToRange(range: Range, color: string) {
    const span = document.createElement('span');
    span.className = `highlight-${this.getColorClass(color)}`;
    span.style.backgroundColor = color;
    
    try {
      range.surroundContents(span);
    } catch (e) {
      // If we can't surround contents, extract and wrap
      const contents = range.extractContents();
      span.appendChild(contents);
      range.insertNode(span);
    }
  }

  private applyHighlights() {
    if (!this.contentBody) return;

    const currentPageData = this.currentPage();
    if (!currentPageData) return;

    const pageHighlights = this.highlights().filter(h => h.pageId === currentPageData.id);
    
    // For now, we'll apply highlights by searching for the text
    // In a production app, you'd want more sophisticated text positioning
    pageHighlights.forEach(highlight => {
      this.applyHighlightByText(highlight);
    });
  }

  private applyHighlightByText(highlight: Highlight) {
    const contentElement = this.contentBody.nativeElement;
    const walker = document.createTreeWalker(
      contentElement,
      NodeFilter.SHOW_TEXT,
      null
    );

    let node;
    while (node = walker.nextNode()) {
      const text = node.textContent || '';
      const index = text.indexOf(highlight.text);
      
      if (index !== -1) {
        const range = document.createRange();
        range.setStart(node, index);
        range.setEnd(node, index + highlight.text.length);
        
        const span = document.createElement('span');
        span.className = `highlight-${this.getColorClass(highlight.color)}`;
        span.style.backgroundColor = highlight.color;
        span.setAttribute('data-highlight-id', highlight.id);
        
        try {
          range.surroundContents(span);
          break; // Only highlight first occurrence
        } catch (e) {
          // Handle cases where range spans multiple elements
          console.warn('Could not apply highlight:', e);
        }
      }
    }
  }

  private getColorClass(color: string): string {
    const colorMap: { [key: string]: string } = {
      '#ffff99': 'yellow',
      '#90ee90': 'green',
      '#87ceeb': 'blue',
      '#ffb6c1': 'pink',
      '#ffd700': 'orange',
      '#dda0dd': 'purple'
    };
    return colorMap[color] || 'yellow';
  }

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  ngOnDestroy() {
    // Cleanup if needed
  }
}
