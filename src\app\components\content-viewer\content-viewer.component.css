.content-viewer {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
}

/* Audio Section */
.audio-section {
  padding: 16px 24px;
  border-bottom: 1px solid #e9ecef;
  background-color: #f8f9fa;
}

.audio-player {
  display: flex;
  align-items: center;
  gap: 16px;
}

.audio-info {
  flex: 1;
}

.audio-title {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.audio-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.btn-audio {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background-color: #007bff;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.btn-audio:hover {
  background-color: #0056b3;
}

.audio-icon {
  font-size: 16px;
}

.audio-progress {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 200px;
}

.progress-bar {
  height: 4px;
  background-color: #e9ecef;
  border-radius: 2px;
  overflow: hidden;
  cursor: pointer;
  position: relative;
}

.progress-fill {
  height: 100%;
  background-color: #007bff;
  transition: width 0.1s ease;
}

.time-display {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #666;
}

/* Content Section */
.content-section {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

.content-body {
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.6;
  color: #333;
  user-select: text;
}

.content-body.text-small {
  font-size: 14px;
}

.content-body.text-normal {
  font-size: 16px;
}

.content-body.text-large {
  font-size: 18px;
}

.content-item {
  margin-bottom: 20px;
}

.content-item h3 {
  color: #2c3e50;
  font-weight: 600;
  margin-bottom: 16px;
  font-size: 1.25em;
}

.content-item p {
  margin-bottom: 12px;
  text-align: justify;
}

.content-item ul {
  margin-left: 20px;
  margin-bottom: 16px;
}

.content-item li {
  margin-bottom: 8px;
}

.content-item strong {
  font-weight: 600;
  color: #2c3e50;
}

.no-content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #666;
  font-style: italic;
}

/* Selection Popup */
.selection-popup {
  position: fixed;
  z-index: 1000;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  padding: 4px;
}

.highlight-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  transition: background-color 0.2s ease;
}

.highlight-btn:hover {
  background-color: #f0f0f0;
}

/* Highlight Styles */
.highlight-yellow {
  background-color: #ffff99 !important;
  padding: 1px 2px;
  border-radius: 2px;
}

.highlight-green {
  background-color: #90ee90 !important;
  padding: 1px 2px;
  border-radius: 2px;
}

.highlight-blue {
  background-color: #87ceeb !important;
  padding: 1px 2px;
  border-radius: 2px;
}

.highlight-pink {
  background-color: #ffb6c1 !important;
  padding: 1px 2px;
  border-radius: 2px;
}

.highlight-orange {
  background-color: #ffd700 !important;
  padding: 1px 2px;
  border-radius: 2px;
}

.highlight-purple {
  background-color: #dda0dd !important;
  padding: 1px 2px;
  border-radius: 2px;
}

/* Scrollbar */
.content-section::-webkit-scrollbar {
  width: 8px;
}

.content-section::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.content-section::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.content-section::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Responsive */
@media (max-width: 768px) {
  .content-section {
    padding: 16px;
  }
  
  .audio-section {
    padding: 12px 16px;
  }
  
  .audio-progress {
    min-width: 150px;
  }
}
